#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_like_collect():
    """测试点赞和收藏功能"""
    base_url = "http://localhost:5000/api"
    
    print("=== 测试点赞和收藏功能 ===")
    
    # 1. 先发布一个动态
    print("\n1. 发布测试动态...")
    post_data = {
        "user_id": 37,
        "content": "测试点赞和收藏功能",
        "type": "dynamic",
        "images": []
    }
    
    try:
        response = requests.post(
            f"{base_url}/cat_circle/posts",
            json=post_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            post_id = result.get('data', {}).get('id')
            print(f"✅ 动态发布成功，ID: {post_id}")
        else:
            print(f"❌ 动态发布失败: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 发布请求失败: {e}")
        return
    
    # 2. 测试点赞
    print(f"\n2. 测试点赞动态 {post_id}...")
    try:
        response = requests.post(
            f"{base_url}/cat_circle/posts/{post_id}/like",
            json={"user_id": 37},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 点赞成功!")
            print(f"点赞状态: {result.get('data', {}).get('is_liked')}")
            print(f"点赞数: {result.get('data', {}).get('like_count')}")
        else:
            print(f"❌ 点赞失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 点赞请求失败: {e}")
    
    # 3. 测试收藏
    print(f"\n3. 测试收藏动态 {post_id}...")
    try:
        response = requests.post(
            f"{base_url}/cat_circle/posts/{post_id}/collect",
            json={"user_id": 37},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 收藏成功!")
            print(f"收藏状态: {result.get('data', {}).get('is_collected')}")
            print(f"收藏数: {result.get('data', {}).get('collect_count')}")
        else:
            print(f"❌ 收藏失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 收藏请求失败: {e}")
    
    # 4. 测试取消点赞
    print(f"\n4. 测试取消点赞...")
    try:
        response = requests.post(
            f"{base_url}/cat_circle/posts/{post_id}/like",
            json={"user_id": 37},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 取消点赞成功!")
            print(f"点赞状态: {result.get('data', {}).get('is_liked')}")
            print(f"点赞数: {result.get('data', {}).get('like_count')}")
        else:
            print(f"❌ 取消点赞失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 取消点赞请求失败: {e}")
    
    # 5. 获取动态详情验证状态
    print(f"\n5. 验证动态状态...")
    try:
        response = requests.get(
            f"{base_url}/cat_circle/posts",
            params={'user_id': 37, 'page': 1, 'per_page': 1},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            posts = result.get('data', {}).get('posts', [])
            if posts:
                post = posts[0]
                print("✅ 动态状态验证:")
                print(f"  点赞状态: {post.get('is_liked')}")
                print(f"  收藏状态: {post.get('is_collected')}")
                print(f"  点赞数: {post.get('like_count')}")
                print(f"  收藏数: {post.get('collect_count')}")
        else:
            print(f"❌ 获取动态失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取动态请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_like_collect()
