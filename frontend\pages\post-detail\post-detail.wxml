<!-- 动态详情页 -->
<view class="page-wrapper">
  <!-- 全局通知组件 -->
  <notification-toast
    show="{{showGlobalNotification}}"
    title="{{globalNotificationTitle}}"
    message="{{globalNotificationMessage}}"
    postId="{{globalNotificationPostId}}"
    bind:hide="onGlobalNotificationHide"
    bind:tap="onGlobalNotificationTap">
  </notification-toast>

  <view class="container">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-left" bindtap="goBack">
      <text class="nav-icon back-icon">‹</text>
    </view>
    <view class="nav-title">详情</view>
    <view class="nav-right">
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view class="content-scroll" scroll-y="{{true}}" enhanced="{{true}}">
    <!-- 动态详情 -->
    <view class="post-detail" wx:if="{{postDetail}}">
      <!-- 用户信息 -->
      <view class="user-info">
        <image class="user-avatar" src="{{postDetail.user_info.avatar || '/images/cat.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">{{postDetail.user_info.nickname || '匿名用户'}}</text>
          <text class="post-time">{{postDetail.created_at}}</text>
        </view>
      </view>

      <!-- 动态内容 -->
      <view class="post-content">
        <text class="content-text">{{postDetail.content}}</text>
      </view>

      <!-- 图片展示 -->
      <view class="images-container" wx:if="{{postDetail.images && postDetail.images.length > 0}}">
        <view class="images-grid">
          <image
            class="post-image"
            wx:for="{{postDetail.images}}"
            wx:key="*this"
            src="{{item}}"
            mode="aspectFill"
            bindtap="previewImage"
            data-current="{{item}}"
            data-urls="{{postDetail.images}}"
          ></image>
        </view>
      </view>

      <!-- 特殊信息 -->
      <view class="extra-info" wx:if="{{postDetail.contact_info || postDetail.gender || postDetail.price || postDetail.location || postDetail.time}}">
        <view class="info-item" wx:if="{{postDetail.price}}">
          <text class="info-label">💰 价格：</text>
          <text class="info-value">{{postDetail.price}}</text>
        </view>
        <view class="info-item" wx:if="{{postDetail.location}}">
          <text class="info-label">📍 地点：</text>
          <text class="info-value">{{postDetail.location}}</text>
        </view>
        <view class="info-item" wx:if="{{postDetail.time}}">
          <text class="info-label">⏰ 时间：</text>
          <text class="info-value">{{postDetail.time}}</text>
        </view>
        <view class="info-item" wx:if="{{postDetail.contact_info}}">
          <text class="info-label">📞 联系：</text>
          <text class="info-value">{{postDetail.contact_info}}</text>
        </view>
        <view class="info-item" wx:if="{{postDetail.gender}}">
          <text class="info-label">👤 性别：</text>
          <text class="info-value">{{postDetail.gender === 'male' ? '男' : '女'}}</text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="post-stats">
        <view class="view-count">
          <text class="view-icon">👁</text>
          <text class="view-text">{{postDetail.view_count || 0}}</text>
        </view>
        <view class="action-buttons">
          <view class="action-btn {{postDetail.is_liked ? 'liked' : ''}}" bindtap="toggleLike">
            <text class="action-icon">❤️</text>
            <text class="action-count">{{postDetail.like_count || 0}}</text>
          </view>
          <view class="action-btn {{postDetail.is_collected ? 'collected' : ''}}" bindtap="toggleCollect">
            <text class="action-icon">⭐</text>
            <text class="action-count">{{postDetail.collect_count || 0}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 评论区域 -->
    <view class="comments-section">
      <view class="comments-header">
        <text class="comments-title">评论 {{comments.length}}</text>
      </view>

      <!-- 评论列表 -->
      <view class="comments-list">
        <view class="comment-item" wx:for="{{comments}}" wx:key="id">
          <!-- 评论内容 -->
          <view class="comment-main">
            <image class="comment-avatar" src="{{item.user_info.avatar || '/images/cat.png'}}" mode="aspectFill"></image>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-user">{{item.user_info.nickname || '匿名用户'}}</text>
                <text class="comment-time">{{item.created_at}}</text>
              </view>
              <text class="comment-text">{{item.content}}</text>
              <view class="comment-actions">
                <text class="reply-btn" bindtap="replyComment" data-id="{{item.id}}" data-user="{{item.user_info.nickname}}">回复</text>
              </view>
            </view>
          </view>

          <!-- 回复列表 -->
          <view class="replies-list" wx:if="{{item.replies && item.replies.length > 0}}">
            <!-- 显示前3条回复 -->
            <view class="reply-item" wx:for="{{item.replies}}" wx:for-item="reply" wx:key="id" wx:if="{{index < 3 || item.showAllReplies}}">
              <text class="reply-user">{{reply.user_info.nickname}}</text>
              <text class="reply-to" wx:if="{{reply.reply_to_user_info}}">回复 {{reply.reply_to_user_info.nickname}}</text>
              <text class="reply-content">：{{reply.content}}</text>
            </view>

            <!-- 展开/收起按钮 -->
            <view class="expand-replies-btn" wx:if="{{item.replies.length > 3}}" bindtap="toggleReplies" data-commentid="{{item.id}}">
              <text class="expand-text" wx:if="{{!item.showAllReplies}}">展开更多回复 ({{item.replies.length - 3}}条)</text>
              <text class="expand-text" wx:else>收起回复</text>
              <text class="expand-icon">{{item.showAllReplies ? '▲' : '▼'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-comments" wx:if="{{comments.length === 0}}">
        <text class="empty-text">暂无评论，快来抢沙发吧~</text>
      </view>
    </view>
  </scroll-view>

  <!-- 评论输入框 -->
  <view class="comment-input-area">
    <view class="input-wrapper">
      <input
        class="comment-input"
        placeholder="{{replyPlaceholder}}"
        value="{{commentContent}}"
        bindinput="onCommentInput"
        confirm-type="send"
        bindconfirm="submitComment"
      />
      <button
        class="send-btn"
        style="background-color: {{commentContent ? '#4C84FF' : '#e5e5e5'}}; color: {{commentContent ? '#fff' : '#999'}}"
        bindtap="submitComment"
        disabled="{{!commentContent}}">发送</button>
    </view>
  </view>
</view>
</view>
