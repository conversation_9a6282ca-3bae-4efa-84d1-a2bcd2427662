/* 页面包装器 */
.page-wrapper {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 状态栏占位 */
.status-bar-placeholder {
  height: env(safe-area-inset-top);
  background-color: #4C84FF;
}

/* 主容器 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  background-color: #4C84FF;
  padding: 0 16px;
  padding-top: 44px;
}

.nav-left, .nav-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.nav-icon {
  font-size: 20px;
  color: #fff;
}

.back-icon {
  font-size: 36px;
  font-weight: bold;
  margin-left: -6px;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-bottom: 60px;
  padding-top: 16px;
}

/* 动态详情 */
.post-detail {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.post-time {
  font-size: 12px;
  color: #999;
}

.post-content {
  margin-bottom: 12px;
}

.content-text {
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}

/* 图片展示 */
.images-container {
  margin-bottom: 12px;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.post-image {
  width: calc((100% - 16px) / 3);
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
}

/* 特殊信息 */
.extra-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

/* 统计信息 */
.post-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.view-count {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.view-icon {
  margin-right: 4px;
}

.view-text {
  margin-left: 2px;
}

.action-buttons {
  display: flex;
  gap: 24px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
  min-width: 50px;
  justify-content: center;
}

.action-btn.liked {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid #ff6b6b;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.2);
}

.action-btn.collected {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid #ffd700;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
}

.action-icon {
  font-size: 18px;
  color: #999;
  transition: all 0.3s ease;
}

.action-count {
  font-size: 14px;
  color: #999;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.liked .action-icon {
  color: #ff6b6b;
}

.action-btn.liked .action-count {
  color: #ff6b6b;
  font-weight: 600;
}

.action-btn.collected .action-icon {
  color: #ffd700;
}

.action-btn.collected .action-count {
  color: #ffd700;
  font-weight: 600;
}

/* 未激活状态的按钮样式 */
.action-btn:not(.liked):not(.collected) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

/* 评论区域 */
.comments-section {
  background-color: #fff;
  padding: 16px;
  margin-top: 8px;
}

.comments-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.comments-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 评论列表 */
.comment-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f8f9fa;
}

.comment-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.comment-main {
  display: flex;
  align-items: flex-start;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-user {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-text {
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  margin-bottom: 8px;
}

.comment-actions {
  display: flex;
  align-items: center;
}

.reply-btn {
  font-size: 12px;
  color: #666;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #f8f9fa;
}

/* 回复列表 */
.replies-list {
  margin-top: 12px;
  padding-left: 44px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

.reply-item {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-user {
  color: #007aff;
  font-weight: 600;
}

.reply-to {
  color: #666;
  margin: 0 4px;
}

.reply-content {
  color: #333;
}

/* 展开回复按钮样式 */
.expand-replies-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  margin-top: 8px;
  background-color: #f0f0f0;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-replies-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.98);
}

.expand-text {
  font-size: 13px;
  color: #666;
  margin-right: 4px;
}

.expand-icon {
  font-size: 12px;
  color: #999;
  transition: transform 0.2s ease;
}

/* 空状态 */
.empty-comments {
  text-align: center;
  padding: 40px 0;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 评论输入框 */
.comment-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-input {
  flex: 1;
  height: 40px;
  background-color: #f8f9fa;
  border-radius: 20px;
  padding: 0 16px;
  font-size: 16px;
  border: 1px solid #e5e5e5;
  color: #333;
}

.comment-input:focus {
  border-color: #e5e5e5;
  background-color: #fff;
}

.comment-input::placeholder {
  color: #999;
  font-size: 14px;
}

.send-btn {
  width: 64px;
  height: 40px;
  background-color: #e5e5e5;
  color: #999;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 有内容时的蓝色状态 - 提高优先级 */
.send-btn.active:not([disabled]) {
  background-color: #4C84FF !important;
  color: #fff !important;
}

/* 无内容时的灰色状态 */
.send-btn[disabled] {
  background-color: #e5e5e5 !important;
  color: #999 !important;
}
