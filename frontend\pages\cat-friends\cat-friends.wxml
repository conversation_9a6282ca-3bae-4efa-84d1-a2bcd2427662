<view class="container">
  <!-- 全局通知组件 -->
  <notification-toast
    show="{{showGlobalNotification}}"
    title="{{globalNotificationTitle}}"
    message="{{globalNotificationMessage}}"
    postId="{{globalNotificationPostId}}"
    bind:hide="onGlobalNotificationHide"
    bind:tap="onGlobalNotificationTap">
  </notification-toast>

  <!-- 分类导航 -->
  <view class="nav-tabs">
    <scroll-view class="nav-tabs-scroll" scroll-x="true">
      <view class="nav-tab-item {{currentTab === 'all' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="all">
        全部
      </view>
      <view class="nav-tab-item {{currentTab === 'dynamic' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="dynamic">
        动态
      </view>
      <view class="nav-tab-item {{currentTab === 'secondhand' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="secondhand">
        二手交易
      </view>
      <view class="nav-tab-item {{currentTab === 'help' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="help">
        求助
      </view>
      <view class="nav-tab-item {{currentTab === 'lost_found' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="lost_found">
        失物招领
      </view>
      <view class="nav-tab-item {{currentTab === 'cat_friend' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="cat_friend">
        猫友
      </view>
      <view class="nav-tab-item {{currentTab === 'campus_run' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="campus_run">
        校园跑
      </view>
    </scroll-view>
    <view class="sort-btn" bindtap="toggleSort">
      <text>{{sortExpanded ? '收起' : '展开'}}</text>
      <text class="arrow {{sortExpanded ? 'up' : 'down'}}">▲</text>
    </view>
  </view>

  <!-- 动态列表 -->
  <scroll-view class="posts-container" scroll-y="true" bindscrolltolower="loadMore">
    <view class="posts-list">
      <view class="post-card" wx:for="{{posts}}" wx:key="id">
        <!-- 顶部标签 -->
        <view class="post-tag" wx:if="{{item.is_featured}}">
          <text class="tag-text">🔥 热门</text>
        </view>

        <!-- 用户信息 -->
        <view class="post-header">
          <image
            class="user-avatar clickable"
            src="{{item.user_info.avatar || '/images/cat.png'}}"
            mode="aspectFill"
            bindtap="goToChat"
            data-user-id="{{item.user_id}}"
            data-nickname="{{item.user_info.nickname}}"
            data-avatar="{{item.user_info.avatar}}">
          </image>
          <view class="user-info">
            <view
              class="user-name clickable"
              bindtap="goToChat"
              data-user-id="{{item.user_id}}"
              data-nickname="{{item.user_info.nickname}}"
              data-avatar="{{item.user_info.avatar}}">
              {{item.user_info.nickname || '匿名用户'}}
            </view>
            <view class="post-time">{{item.created_at}}</view>
          </view>
          <view class="more-btn" bindtap="showMoreOptions" data-id="{{item.id}}">
            <text class="more-icon">⋮</text>
          </view>
        </view>

        <!-- 动态内容 -->
        <view class="post-content">
          <!-- 标题（如果有） -->
          <view class="post-title" wx:if="{{item.title}}">
            <text class="title-text">{{item.title}}</text>
          </view>

          <!-- 内容文本 -->
          <view class="content-wrapper">
            <text class="content-text {{!item.expanded && item.content && item.content.length > 40 ? 'content-text-collapsed' : ''}}">{{item.content || ''}}</text>
            <text class="expand-btn" wx:if="{{item.content && item.content.length > 40}}" bindtap="toggleExpand" data-id="{{item.id}}">
              {{item.expanded ? '收起' : '展开'}}
            </text>
          </view>

          <!-- 图片展示 -->
          <view class="images-container" wx:if="{{item.images && item.images.length > 0}}">
            <view class="images-grid images-{{item.images.length > 3 ? 'multi' : item.images.length}}">
              <image
                class="post-image"
                wx:for="{{item.images}}"
                wx:for-item="image"
                wx:for-index="imgIndex"
                wx:key="*this"
                src="{{image}}"
                mode="aspectFill"
                bindtap="previewImage"
                data-current="{{image}}"
                data-urls="{{item.images}}"
              ></image>
            </view>
          </view>

          <!-- 特殊信息 -->
          <view class="extra-info" wx:if="{{item.contact_info || item.gender || item.price}}">
            <view class="info-item" wx:if="{{item.contact_info}}">
              <text class="info-label">联系方式：</text>
              <text class="info-value">{{item.contact_info}}</text>
            </view>
            <view class="info-item" wx:if="{{item.gender}}">
              <text class="info-label">性别：</text>
              <text class="info-value">{{item.gender === 'male' ? '男' : '女'}}</text>
            </view>
            <view class="info-item" wx:if="{{item.price}}">
              <text class="info-label">价格：</text>
              <text class="info-value price">¥{{item.price}}</text>
            </view>
          </view>
        </view>

        <!-- 互动区域 -->
        <view class="post-footer">
          <view class="view-count">
            <text class="view-icon">👁</text>
            <text class="view-text">{{item.view_count || 5609}}</text>
          </view>
          <view class="action-buttons">
            <view class="action-btn comment-btn" bindtap="goToPostDetail" data-id="{{item.id}}">
              <text class="action-icon">💬</text>
              <text class="action-count">{{item.comment_count || 0}}</text>
              <!-- 未读消息数量提示 -->
              <view class="unread-badge" wx:if="{{item.unread_comment_count > 0}}">
                <text class="unread-count">{{item.unread_comment_count}}</text>
              </view>
            </view>
            <view class="action-btn {{item.is_liked ? 'liked' : ''}}" bindtap="toggleLike" data-id="{{item.id}}">
              <text class="action-icon" wx:if="{{item.is_liked}}">❤️</text>
              <text class="action-icon" wx:else>🤍</text>
              <text class="action-count">{{item.like_count || 0}}</text>
            </view>
            <view class="action-btn {{item.is_collected ? 'collected' : ''}}" bindtap="toggleCollect" data-id="{{item.id}}">
              <text class="action-icon" wx:if="{{item.is_collected}}">⭐</text>
              <text class="ic action-icon" wx:else>☆</text>
              <text class="action-count">{{item.collect_count || 0}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && posts.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{posts.length === 0 && !loading}}">
      <text class="empty-text">暂无动态，快来发布第一条吧！</text>
    </view>
  </scroll-view>

  <!-- 发布按钮 -->
  <movable-area class="movable-area">
    <movable-view
      class="floating-btn {{isDragging ? 'dragging' : ''}}"
      direction="all"
      bindtap="showPublishModal"
      bindtouchstart="onDragStart"
      bindtouchend="onDragEnd"
      x="{{fabX}}"
      y="{{fabY}}"
      out-of-bounds="true"
      damping="20"
      friction="2">
      <text class="fab-icon">+</text>
    </movable-view>
  </movable-area>
</view>

<!-- 发布弹窗 -->
<view class="modal-overlay" wx:if="{{showPublishModal}}" bindtap="hidePublishModal">
  <view class="publish-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">发布动态</text>
      <text class="modal-close" bindtap="hidePublishModal">×</text>
    </view>

    <scroll-view class="modal-content" scroll-y="true" enhanced="{{true}}" show-scrollbar="{{false}}" catchtap="stopPropagation">
      <!-- 发布类型选择 -->
      <view class="type-selector" catchtap="stopPropagation">
        <text class="section-label">选择类型</text>
        <view class="type-grid">
          <view
            class="type-item {{publishType === item.value ? 'active' : ''}}"
            wx:for="{{typeOptions}}"
            wx:key="value"
            bindtap="selectType"
            data-type="{{item.value}}"
            catchtap="selectType">
            <text class="type-icon">{{item.icon}}</text>
            <text class="type-name">{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 选择提示 -->
      <view class="select-tip" wx:if="{{!publishType}}">
        <view class="tip-content">
          <view class="tip-icon">💡</view>
          <view class="tip-text">请先选择要发布的内容类型</view>
          <view class="tip-subtitle">选择类型后将显示对应的发布表单</view>
        </view>
      </view>

      <!-- 输入框提示 -->
      <view class="input-guide" wx:if="{{publishType && !publishContent}}">
        <view class="guide-content">
          <view class="guide-icon">✏️</view>
          <view class="guide-text">在下方输入框中填写{{getContentLabel(publishType).replace('✍️ ', '').replace('📝 ', '').replace('📢 ', '').replace('🔍 ', '').replace('👋 ', '').replace('🏃 ', '')}}</view>
          <view class="guide-subtitle">详细的内容描述有助于获得更多关注</view>
        </view>
      </view>

      <!-- 内容输入 -->
      <view class="content-section" wx:if="{{publishType}}">
        <view class="section-header">
          <text class="section-label">{{getContentLabel(publishType)}}</text>
          <text class="section-required">*必填</text>
        </view>
        <view class="input-wrapper">
          <textarea
            class="content-input"
            placeholder="{{getContentPlaceholder(publishType)}}"
            value="{{publishContent}}"
            bindinput="onContentInput"
            maxlength="500"
            auto-height="{{true}}"
            show-confirm-bar="{{false}}"
          ></textarea>
          <view class="input-tips" wx:if="{{!publishContent}}">
            <view class="tip-item">
              <text class="tip-icon-small">💡</text>
              <text class="tip-text-small">{{getInputTips(publishType)}}</text>
            </view>
          </view>
        </view>
        <view class="char-count">{{publishContent.length}}/500</view>
      </view>

      <!-- 图片上传 -->
      <view class="image-section">
        <text class="section-label">添加图片（最多10张）</text>
        <view class="upload-grid">
          <view class="upload-item" wx:for="{{publishImages}}" wx:key="*this">
            <image class="upload-image" src="{{item}}" mode="aspectFill"></image>
            <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>
          <view class="upload-btn" wx:if="{{publishImages.length < 10}}" bindtap="chooseImage">
            <text class="upload-icon">+</text>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
      </view>

      <!-- 价格输入（二手交易） -->
      <view class="price-section" wx:if="{{publishType === 'secondhand'}}">
        <text class="section-label">商品价格</text>
        <view class="price-input-wrapper">
          <text class="currency">¥</text>
          <input
            class="price-input"
            type="digit"
            placeholder="请输入价格"
            value="{{publishPrice}}"
            bindinput="onPriceInput"
          />
        </view>
      </view>

      <!-- 联系方式（特定类型显示） -->
      <view class="contact-section" wx:if="{{publishType === 'secondhand' || publishType === 'help' || publishType === 'lost_found' || publishType === 'campus_run'}}">
        <text class="section-label">联系方式</text>
        <input
          class="contact-input"
          placeholder="{{getContactPlaceholder(publishType)}}"
          value="{{publishContact}}"
          bindinput="onContactInput"
        />
      </view>

      <!-- 性别选择（猫友类型显示） -->
      <view class="gender-section" wx:if="{{publishType === 'cat_friend'}}">
        <text class="section-label">性别</text>
        <view class="gender-options">
          <view
            class="gender-item {{publishGender === 'male' ? 'active' : ''}}"
            bindtap="selectGender"
            data-gender="male">
            <text class="gender-icon">👨</text>
            <text class="gender-text">男</text>
          </view>
          <view
            class="gender-item {{publishGender === 'female' ? 'active' : ''}}"
            bindtap="selectGender"
            data-gender="female">
            <text class="gender-icon">👩</text>
            <text class="gender-text">女</text>
          </view>
        </view>
      </view>

      <!-- 地点信息（失物招领、校园跑） -->
      <view class="location-section" wx:if="{{publishType === 'lost_found' || publishType === 'campus_run'}}">
        <text class="section-label">{{publishType === 'lost_found' ? '丢失地点' : '出发地点'}}</text>
        <input
          class="location-input"
          placeholder="{{publishType === 'lost_found' ? '请输入丢失地点' : '请输入出发地点'}}"
          value="{{publishLocation}}"
          bindinput="onLocationInput"
        />
      </view>

      <!-- 时间选择（校园跑） -->
      <view class="time-section" wx:if="{{publishType === 'campus_run'}}">
        <text class="section-label">出发时间</text>
        <picker mode="multiSelector" bindchange="onTimeChange" value="{{timeIndex}}" range="{{timeRange}}">
          <view class="time-picker">
            <text>{{selectedTime || '请选择时间'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </scroll-view>

    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hidePublishModal">取消</button>
      <button class="publish-btn" bindtap="submitPost" disabled="{{publishing || !canPublish}}">
        {{publishing ? '发布中...' : '发布'}}
      </button>
    </view>
  </view>
</view>

<!-- 评论弹窗 -->
<view class="modal-overlay" wx:if="{{showCommentModal}}" bindtap="hideCommentModal">
  <view class="comment-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">评论</text>
      <text class="modal-close" bindtap="hideCommentModal">×</text>
    </view>

    <view class="modal-content">
      <!-- 评论列表 -->
      <scroll-view class="comments-list" scroll-y="true">
        <view class="comment-item" wx:for="{{comments}}" wx:key="id">
          <view class="comment-header">
            <image class="comment-avatar" src="{{item.user_info.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="comment-info">
              <text class="comment-user">{{item.user_info.nickname || '匿名用户'}}</text>
              <text class="comment-time">{{item.created_at}}</text>
            </view>
            <text class="reply-btn" bindtap="replyComment" data-id="{{item.id}}" data-user="{{item.user_info.nickname}}">回复</text>
          </view>
          <view class="comment-content">
            <text class="comment-text">{{item.content}}</text>
          </view>

          <!-- 回复列表 -->
          <view class="replies-list" wx:if="{{item.replies && item.replies.length > 0}}">
            <view class="reply-item" wx:for="{{item.replies}}" wx:for-item="reply" wx:key="id">
              <text class="reply-user">{{reply.user_info.nickname}}</text>
              <text class="reply-to" wx:if="{{reply.reply_to_user_info}}">回复 {{reply.reply_to_user_info.nickname}}</text>
              <text class="reply-content">：{{reply.content}}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 评论输入 -->
      <view class="comment-input-area">
        <input
          class="comment-input"
          placeholder="{{replyPlaceholder}}"
          value="{{commentContent}}"
          bindinput="onCommentInput"
          confirm-type="send"
          bindconfirm="submitComment"
        />
        <button class="send-btn" bindtap="submitComment" disabled="{{!commentContent.trim()}}">发送</button>
      </view>
    </view>
  </view>
</view>

<!-- 悬浮通话组件 -->
<floating-call
  show="{{showFloatingCall}}"
  call-info="{{currentCall}}">
</floating-call>