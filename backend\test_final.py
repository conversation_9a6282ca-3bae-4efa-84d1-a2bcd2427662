#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_cat_circle():
    """测试猫友圈完整功能"""
    base_url = "http://localhost:5000/api"
    
    print("=== 测试猫友圈API ===")
    
    # 1. 测试发布动态
    print("\n1. 测试发布动态...")
    post_data = {
        "user_id": 37,
        "content": "测试发布一个动态内容",
        "type": "dynamic",
        "images": []
    }
    
    try:
        response = requests.post(
            f"{base_url}/cat_circle/posts",
            json=post_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 发布成功!")
            print(f"返回数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            post_id = result.get('data', {}).get('id')
        else:
            print(f"❌ 发布失败: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 发布请求失败: {e}")
        return
    
    # 2. 测试获取动态列表
    print("\n2. 测试获取动态列表...")
    try:
        response = requests.get(
            f"{base_url}/cat_circle/posts",
            params={'user_id': 37, 'page': 1, 'per_page': 10},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取列表成功!")
            posts = result.get('data', {}).get('posts', [])
            print(f"获取到 {len(posts)} 条动态")
            if posts:
                print(f"第一条动态: {posts[0].get('content', '')[:50]}...")
        else:
            print(f"❌ 获取列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取列表请求失败: {e}")
    
    # 3. 测试二手交易发布
    print("\n3. 测试二手交易发布...")
    secondhand_data = {
        "user_id": 37,
        "content": "出售二手书籍，九成新",
        "type": "secondhand",
        "images": [],
        "price": "50",
        "contact_info": "微信：test123"
    }
    
    try:
        response = requests.post(
            f"{base_url}/cat_circle/posts",
            json=secondhand_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 二手交易发布成功!")
            print(f"内容: {result.get('data', {}).get('content', '')}")
        else:
            print(f"❌ 二手交易发布失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 二手交易发布请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_cat_circle()
