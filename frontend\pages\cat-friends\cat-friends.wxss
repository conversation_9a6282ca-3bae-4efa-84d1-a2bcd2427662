/* 猫友圈页面样式 */
.container {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 分类导航 */
.nav-tabs {
  display: flex;
  background: white;
  border-bottom: 1rpx solid #e9ecef;
  padding: 10rpx 0;
}

.nav-tabs-scroll {
  flex: 1;
  white-space: nowrap;
}

.nav-tab-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 30rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.nav-tab-item.active {
  background: #ffd700;
  color: #333;
  font-weight: 500;
}

.sort-btn {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #666;
}

.arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
  transition: transform 0.3s ease;
}

.arrow.up {
  transform: rotate(0deg);
}

.arrow.down {
  transform: rotate(180deg);
}

/* 动态列表 */
.posts-container {
  flex: 1;
  background: #f8f9fa;
}

.posts-list {
  padding: 20rpx 30rpx;
}

/* 动态卡片 */
.post-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 热门标签 */
.post-tag {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  z-index: 2;
}

.tag-text {
  font-weight: 500;
}

/* 用户信息 */
.post-header {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.clickable {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.clickable:active {
  opacity: 0.7;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.more-btn {
  padding: 10rpx;
}

.more-icon {
  font-size: 32rpx;
  color: #999;
}

/* 动态内容 */
.post-content {
  padding: 0 30rpx 20rpx;
}

.post-title {
  margin-bottom: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.content-wrapper {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

/* 收起状态的文本样式 */
.content-text-collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 显示1行，大约20个字 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.4em; /* 确保只显示一行多一点的高度 */
}

.expand-btn {
  color: #007aff;
  font-size: 26rpx;
  margin-left: 10rpx;
}

/* 图片展示 */
.images-container {
  margin: 20rpx 0;
}

.images-grid {
  display: grid;
  gap: 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.images-1 {
  grid-template-columns: 1fr;
}

.images-2 {
  grid-template-columns: 1fr 1fr;
}

.images-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.images-multi {
  grid-template-columns: 1fr 1fr 1fr;
}

.post-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.images-1 .post-image {
  height: 400rpx;
}

.images-2 .post-image {
  height: 240rpx;
}

/* 特殊信息 */
.extra-info {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.info-value.price {
  color: #ff6b6b;
  font-weight: 600;
}

/* 底部交互区域 */
.post-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.view-count {
  display: flex;
  align-items: center;
  color: #999;
}

.view-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.view-text {
  font-size: 24rpx;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx 20rpx;
  border-radius: 50rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 60rpx;
  min-height: 60rpx;
}

.action-btn:active {
  transform: scale(0.9);
}

/* 点赞按钮状态 */
.action-btn.liked {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
  border-color: #ff5252;
  box-shadow: 0 4rpx 12rpx rgba(255, 82, 82, 0.3);
  animation: likeAnimation 0.6s ease-in-out;
}

.action-btn.liked .action-icon {
  color: white;
  transform: scale(1.1);
}

.action-btn.liked .action-count {
  color: white;
}

/* 收藏按钮状态 */
.action-btn.collected {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border-color: #ffd700;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
  animation: collectAnimation 0.6s ease-in-out;
}

.action-btn.collected .action-icon {
  color: #333;
  transform: scale(1.1);
}

.action-btn.collected .action-count {
  color: #333;
}

.action-icon {
  font-size: 32rpx;
  color: #666;
  transition: all 0.3s ease;
}

.action-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 评论按钮样式 */
.comment-btn {
  position: relative;
}

/* 未读消息数量提示 */
.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #FF4444;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.unread-count {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  padding: 0 6rpx;
}

/* 动画效果 */
@keyframes likeAnimation {
  0% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes collectAnimation {
  0% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.2) rotate(5deg); }
  50% { transform: scale(1) rotate(0deg); }
  75% { transform: scale(1.1) rotate(-5deg); }
  100% { transform: scale(1) rotate(0deg); }
}

/* 加载状态 */
.loading-more, .no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
}

/* 发布按钮 */
.movable-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 允许点击穿透 */
}

.floating-btn {
  margin: -90rpx 0;
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(45deg, #4dabf7, #339af0); /* 蓝色背景 */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(51, 154, 240, 0.5); /* 蓝色阴影 */
  pointer-events: auto; /* 按钮本身可以点击 */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  right: 40rpx;
  bottom: 220rpx; /* 再向上移动一点 */
}

.floating-btn.dragging {
  transform: scale(1.1);
  box-shadow: 0 16rpx 30rpx rgba(51, 154, 240, 0.6); /* 蓝色阴影 */
}

.fab-icon {
  font-size: 80rpx; /* 增大加号尺寸 */
  color: white;
  font-weight: 400; /* 适中的字重 */
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 15vh;
  z-index: 1000;
}

.publish-modal, .comment-modal {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-content {
  flex: 1;
  height: 0;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .publish-btn {
  flex: 1;
  padding: 28rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  text-align: center;
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.cancel-btn:active {
  background: #e9ecef;
}

.publish-btn {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}

.publish-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.publish-btn[disabled] {
  background: #ddd;
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 新版发布表单样式 */
.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 类型选择网格 */
.type-selector {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 选择提示 */
.select-tip {
  padding: 50rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #e9ecef;
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.tip-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.tip-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.tip-subtitle {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
}

/* 输入框提示 */
.input-guide {
  padding: 30rpx;
  background: linear-gradient(135deg, #fff8e1 0%, #fffbf0 100%);
  border-bottom: 1rpx solid #f0f0f0;
  border-left: 4rpx solid #ffd700;
}

.guide-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.guide-icon {
  font-size: 32rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.guide-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.guide-subtitle {
  font-size: 24rpx;
  color: #666;
  opacity: 0.9;
  line-height: 1.3;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx 15rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 120rpx;
  justify-content: center;
}

.type-item.active {
  border-color: #ffd700;
  background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.2);
  transform: translateY(-2rpx);
}

.type-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.type-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

/* 内容区域 */
.content-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.section-required {
  font-size: 22rpx;
  color: #ff4757;
  background: #fff5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #ffebee;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.input-tips {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  right: 24rpx;
  pointer-events: none;
  z-index: 1;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  background: rgba(255, 215, 0, 0.1);
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 215, 0, 0.2);
}

.tip-icon-small {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.tip-text-small {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.content-input {
  width: 100%;
  min-height: 240rpx;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background: white;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.content-input:focus {
  border-color: #ffd700;
  outline: none;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 图片区域 */
.image-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.image-section .section-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.image-section .section-label::before {
  content: '📷';
  margin-right: 12rpx;
  font-size: 24rpx;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.upload-item {
  position: relative;
  aspect-ratio: 1;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.upload-btn {
  aspect-ratio: 1;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 价格区域 */
.price-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.price-section .section-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.price-section .section-label::before {
  content: '💰';
  margin-right: 12rpx;
  font-size: 24rpx;
}

.price-input-wrapper {
  display: flex;
  align-items: center;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  background: white;
  margin-top: 20rpx;
  transition: border-color 0.3s ease;
}

.price-input-wrapper:focus-within {
  border-color: #ffd700;
}

.currency {
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-right: 1rpx solid #e9ecef;
  font-weight: 600;
}

.price-input {
  flex: 1;
  padding: 20rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

/* 联系方式区域 */
.contact-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.contact-section .section-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.contact-section .section-label::before {
  content: '📞';
  margin-right: 12rpx;
  font-size: 24rpx;
}

.contact-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 28rpx;
  background: white;
  margin-top: 20rpx;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.contact-input:focus {
  border-color: #ffd700;
  outline: none;
}

/* 性别选择区域 */
.gender-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.gender-section .section-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.gender-section .section-label::before {
  content: '👤';
  margin-right: 12rpx;
  font-size: 24rpx;
}

.gender-options {
  display: flex;
  gap: 30rpx;
  margin-top: 20rpx;
}

.gender-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.gender-item.active {
  border-color: #ffd700;
  background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.2);
  transform: translateY(-2rpx);
}

.gender-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.gender-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}

/* 地点区域 */
.location-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  margin-top: 20rpx;
  box-sizing: border-box;
}

/* 时间选择区域 */
.time-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  background: white;
  margin-top: 20rpx;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}




