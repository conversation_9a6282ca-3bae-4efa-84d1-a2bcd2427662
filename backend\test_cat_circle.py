#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 测试猫友圈API
def test_cat_circle_api():
    base_url = "http://localhost:5000/api"
    
    # 测试数据
    test_post = {
        "user_id": 1,
        "content": "测试发布一个动态",
        "type": "dynamic",
        "images": []
    }
    
    print("测试猫友圈发布API...")
    
    try:
        # 发送POST请求
        response = requests.post(
            f"{base_url}/cat_circle/posts",
            json=test_post,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ API测试成功!")
        else:
            print("❌ API测试失败!")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 测试出错: {e}")

if __name__ == "__main__":
    test_cat_circle_api()
