#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_post_detail_api():
    base_url = "http://localhost:5000/api"
    
    print("测试动态详情页面相关API...")
    
    # 测试用户ID
    user_id = 1
    
    try:
        # 1. 首先获取动态列表，找到一个动态ID
        print("\n1. 获取动态列表...")
        response = requests.get(
            f"{base_url}/cat_circle/posts",
            params={'user_id': user_id, 'page': 1, 'per_page': 10}
        )
        
        if response.status_code == 200:
            data = response.json()
            posts = data.get('data', {}).get('posts', [])
            if posts:
                post_id = posts[0]['id']
                print(f"✅ 获取到动态ID: {post_id}")
                
                # 2. 测试获取动态详情
                print(f"\n2. 测试获取动态详情 (ID: {post_id})...")
                # 这里我们直接从列表中获取详情，实际应用中可能需要单独的详情API
                post_detail = posts[0]
                print(f"✅ 动态详情: {post_detail.get('content', '')[:50]}...")
                
                # 3. 测试获取评论
                print(f"\n3. 测试获取评论...")
                comments_response = requests.get(
                    f"{base_url}/cat_circle/posts/{post_id}/comments",
                    params={'user_id': user_id}
                )
                
                if comments_response.status_code == 200:
                    comments_data = comments_response.json()
                    comments = comments_data.get('data', {}).get('comments', [])
                    print(f"✅ 获取到 {len(comments)} 条评论")
                else:
                    print(f"❌ 获取评论失败: {comments_response.text}")
                
                # 4. 测试发布评论
                print(f"\n4. 测试发布评论...")
                comment_data = {
                    "user_id": user_id,
                    "post_id": post_id,
                    "content": "这是一条测试评论"
                }
                
                comment_response = requests.post(
                    f"{base_url}/cat_circle/comments",
                    json=comment_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if comment_response.status_code == 200:
                    print("✅ 评论发布成功")
                    comment_result = comment_response.json()
                    comment_id = comment_result.get('data', {}).get('id')
                    
                    # 5. 测试回复评论
                    if comment_id:
                        print(f"\n5. 测试回复评论 (ID: {comment_id})...")
                        reply_data = {
                            "user_id": user_id,
                            "post_id": post_id,
                            "content": "这是一条测试回复",
                            "reply_to_comment_id": comment_id
                        }
                        
                        reply_response = requests.post(
                            f"{base_url}/cat_circle/comments",
                            json=reply_data,
                            headers={'Content-Type': 'application/json'}
                        )
                        
                        if reply_response.status_code == 200:
                            print("✅ 回复评论成功")
                        else:
                            print(f"❌ 回复评论失败: {reply_response.text}")
                else:
                    print(f"❌ 评论发布失败: {comment_response.text}")
                
                # 6. 测试点赞功能
                print(f"\n6. 测试点赞功能...")
                like_response = requests.post(
                    f"{base_url}/cat_circle/posts/{post_id}/like",
                    json={"user_id": user_id},
                    headers={'Content-Type': 'application/json'}
                )
                
                if like_response.status_code == 200:
                    like_result = like_response.json()
                    is_liked = like_result.get('data', {}).get('is_liked', False)
                    like_count = like_result.get('data', {}).get('like_count', 0)
                    print(f"✅ 点赞操作成功: {'已点赞' if is_liked else '已取消点赞'}, 总点赞数: {like_count}")
                else:
                    print(f"❌ 点赞操作失败: {like_response.text}")
                
                # 7. 测试收藏功能
                print(f"\n7. 测试收藏功能...")
                collect_response = requests.post(
                    f"{base_url}/cat_circle/posts/{post_id}/collect",
                    json={"user_id": user_id},
                    headers={'Content-Type': 'application/json'}
                )
                
                if collect_response.status_code == 200:
                    collect_result = collect_response.json()
                    is_collected = collect_result.get('data', {}).get('is_collected', False)
                    collect_count = collect_result.get('data', {}).get('collect_count', 0)
                    print(f"✅ 收藏操作成功: {'已收藏' if is_collected else '已取消收藏'}, 总收藏数: {collect_count}")
                else:
                    print(f"❌ 收藏操作失败: {collect_response.text}")
                
                print(f"\n🎉 所有测试完成!")
                
            else:
                print("❌ 没有找到动态数据")
        else:
            print(f"❌ 获取动态列表失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 测试出错: {e}")

if __name__ == "__main__":
    test_post_detail_api()
