<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}大数据题库管理后台{% endblock %}</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Favicon with cache-busting parameter -->
    <link rel="icon" type="image/png" href="/static/img/favicon.ico?v=2">
    
    <!-- Custom CSS with cache-busting parameter -->
    <link href="{{ url_for('static', filename='admin.css') }}?v=2" rel="stylesheet">
    
    <!-- Enhanced Admin Styles -->
    <link href="{{ url_for('static', filename='admin-enhanced.css') }}?v=1" rel="stylesheet">
    
    <!-- User Online Status CSS -->
    <link href="{{ url_for('static', filename='css/user-status.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
    <style>
        :root {
            --primary-color: #4c51bf;
            --primary-rgb: 76, 81, 191;
            --secondary-color: #6c757d;
            --success-color: #0ca678;
            --info-color: #3abff8;
            --warning-color: #f59f00;
            --danger-color: #e53e3e;
            --light-color: #f8f9fa;
            --dark-color: #4a5568;
            --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            --gray-100: #f8fafc;
            --gray-200: #edf2f7;
            --gray-300: #e2e8f0;
            --gray-400: #cbd5e0;
            --gray-500: #a0aec0;
            --gray-600: #718096;
            --gray-700: #4a5568;
            --gray-800: #2d3748;
            --gray-900: #1a202c;
        }

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--gray-100);
            color: var(--gray-800);
            font-size: 0.875rem;
            min-height: 100vh;
            margin: 0;
            line-height: 1.5;
        }

        a {
            text-decoration: none;
            color: var(--primary-color);
            transition: color 0.2s ease;
        }

        a:hover {
            color: #434190;
        }

        /* Layout */
        .wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #5a67d8, #434190);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
            transition: all 0.3s;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar::-webkit-scrollbar {
            width: 5px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 1.75rem 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 0.5rem;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 0.9rem 1.2rem;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            border-radius: 0 4px 4px 0;
            margin: 0.2rem 0.5rem 0.2rem 0;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.12);
            border-left-color: white;
            transform: translateX(3px);
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.18);
            border-left-color: white;
            font-weight: 600;
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        /* Content */
        .content {
            flex: 1;
            margin-left: 250px;
            width: calc(100% - 250px);
            min-height: 100vh;
            background-color: var(--gray-100);
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
        }

        .topbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 1.75rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .topbar h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            display: flex;
            align-items: center;
        }

        .topbar h1 i {
            margin-right: 0.75rem;
            color: var(--primary-color);
        }

        .topbar .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            background-color: var(--gray-100);
            border-radius: 50px;
            padding: 0.4rem 1rem 0.4rem 0.4rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .user-info:hover {
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }

        .user-info .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: 600;
        }

        .user-info span {
            font-weight: 600;
            color: var(--gray-700);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            padding: 1.75rem;
        }

        /* Card Styles */
        .card {
            background-color: white;
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-3px);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .card-header i {
            margin-right: 0.75rem;
            color: var(--primary-color);
        }

        /* Responsive Sidebar Toggle */
        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-600);
            font-size: 1.25rem;
            display: none;
            cursor: pointer;
            padding: 0.5rem;
            transition: color 0.2s;
        }
        
        .sidebar-toggle:hover {
            color: var(--gray-800);
        }
        
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .content {
                margin-left: 0;
                width: 100%;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
        }
    </style>
    {% block styles %}{% endblock %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h3>题库管理系统</h3>
            </div>

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.path == '/admin' %}active{% endif %}" href="/admin">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == '/admin/users' %}active{% endif %}" href="/admin/users">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == '/admin/statistics' %}active{% endif %}" href="/admin/statistics">
                        <i class="fas fa-chart-bar"></i> 统计分析
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/admin/classes' in request.path %}active{% endif %}" href="/admin/classes">
                        <i class="fas fa-graduation-cap"></i> 班级管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/admin/courses' in request.path %}active{% endif %}" href="/admin/courses">
                        <i class="fas fa-book"></i> 课程管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/admin/question-banks' in request.path %}active{% endif %}" href="/admin/question-banks">
                        <i class="fas fa-book-open"></i> 题库管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == '/admin/questions' %}active{% endif %}" href="/admin/questions">
                        <i class="fas fa-question-circle"></i> 题目管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/admin/login-records' in request.path %}active{% endif %}" href="/admin/login-records">
                        <i class="fas fa-history"></i> 登录记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/logout">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div class="content">
            <div class="topbar">
                <h1>{% block header %}大数据题库管理后台{% endblock %}</h1>
                <div class="user-info">
                    <span>{{ admin.username if admin else 'Admin' }}</span>
                    <div class="avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>

            <div class="main-content">
                {% block content %}{% endblock %}
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="copyright text-center">
                        &copy; 2024 大数据题库管理系统. All rights reserved.
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- User Online Status JS -->
    <script src="{{ url_for('static', filename='js/user-status.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading overlay when page is loaded
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }

            // Helper function to show loading overlay
            window.showLoading = function() {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'flex';
                }
            };

            // Add loading indicator to all internal links
            document.querySelectorAll('a[href^="/"]:not([target="_blank"])').forEach(link => {
                link.addEventListener('click', function() {
                    showLoading();
                });
            });

            // Add loading indicator to form submissions
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    showLoading();
                });
            });
            
            // 全局模态框修复 - 防止模态框闪烁和无限循环问题
            $(document).ready(function() {
                // 防止事件冒泡导致的闪烁和无限循环
                $(document).on('show.bs.modal', '.modal', function(event) {
                    event.stopPropagation();
                });
                
                // 防止模态框在显示时被额外事件触发
                $(document).on('shown.bs.modal', '.modal', function(event) {
                    $(this).data('bs.modal')._isShown = true;
                    $(this).attr('data-modal-shown', 'true');
                });
                
                // 确保模态框正确关闭
                $(document).on('hide.bs.modal', '.modal', function(event) {
                    $(this).removeAttr('data-modal-shown');
                });
                
                // 模态框点击事件阻止冒泡
                $(document).on('click', '.modal-dialog', function(e) {
                    e.stopPropagation();
                });
                
                // 提交删除表单时禁用按钮防止重复提交
                $(document).on('submit', '.delete-form', function() {
                    $(this).find('button[type="submit"]').prop('disabled', true)
                        .html('<i class="fas fa-spinner fa-spin me-1"></i>处理中...');
                });
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html> 